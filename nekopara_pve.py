

import os
import random
import time
import textwrap

# --- 核心设定：惩罚卡牌 ---
PENALTY_CARD_PLAYER = {"name": "围观群众", "cost": 0, "type": "penalty", "description": "一张无用的卡。当手牌中集齐5张时，你的罪行将暴露，导致游戏失败。强行打出会失去20生命（无视护盾）。"}
PENALTY_CARD_ENEMY = {"name": "发情", "cost": 0, "type": "status", "description": "一张无用的卡。每在手牌中存在一张，回合结束时因无法抑制的生理反应而失去2点生命（无视护盾）。强行打出会失去6生命（无视护盾）。"}


# --- 核心设定：角色、场景、卡牌库 ---

CATGIRLS = {
    "白音 (Shirone)": {
        "description": "一只警惕而迅捷的白猫，总是在图书馆的阳光下打盹。她的动作如同影子般难以捕捉，擅长用速度和闪避消耗对手的耐心。",
        "hp": 60,
        "base_cards": [
            {"name": "飞扑", "cost": 2, "type": "attack", "value": 8, "description": "对你造成8点伤害。"},
            {"name": "猫毛过敏", "cost": 1, "type": "skill", "description": "在你的回合开始时，对你造成3点持续性伤害，持续2回合。"},
            {"name": "蜷缩", "cost": 1, "type": "defense", "value": 10, "description": "获得10点护盾。"},
            {"name": "尖叫", "cost": 2, "type": "skill", "description": "你的攻击力在下回合降低2点。"},
        ],
        "cards": None  # 将在初始化时生成50张卡
    },
    "黑江 (Kuroe)": {
        "description": "潜伏在公园阴影中的黑猫，眼神中充满了野性与挑衅。她的攻击充满了力量，喜欢正面冲突，用利爪撕裂一切障碍。",
        "hp": 80,
        "base_cards": [
            {"name": "猛抓", "cost": 2, "type": "attack", "value": 12, "description": "对你造成12点伤害。"},
            {"name": "野性威慑", "cost": 3, "type": "attack", "value": 15, "description": "对你造成15点伤害。"},
            {"name": "硬化皮肤", "cost": 2, "type": "defense", "value": 8, "description": "获得8点护盾。"},
            {"name": "狩猎直觉", "cost": 1, "type": "skill", "description": "她的下一次攻击额外造成3点伤害。"},
        ],
        "cards": None  # 将在初始化时生成50张卡
    },
    "橘瑠 (Tachiru)": {
        "description": "一只慵懒而粘人的橘猫，经常出现在游泳馆的更衣室里。她看起来毫无防备，却擅长用各种撒娇和妨害的技巧，让你在不知不觉中陷入她的节奏。",
        "hp": 70,
        "base_cards": [
            {"name": "撒娇", "cost": 1, "type": "skill", "description": "随机弃掉你的一张手牌。"},
            {"name": "猫步", "cost": 2, "type": "defense", "value": 5, "shield": 5, "description": "获得5点生命和5点护盾。"},
            {"name": "绊倒", "cost": 2, "type": "attack", "value": 6, "description": "对你造成6点伤害。"},
            {"name": "湿漉漉的眼神", "cost": 3, "type": "skill", "description": "你在接下来的2回合内无法获得护盾。"},
        ],
        "cards": None  # 将在初始化时生成50张卡
    }
}

# 为每个猫娘生成50张卡的牌库
def generate_enemy_deck(base_cards):
    """根据基础卡牌生成50张卡的牌库"""
    return (
        [base_cards[0]] * 15 +  # 第一张卡 x15
        [base_cards[1]] * 10 +  # 第二张卡 x10
        [base_cards[2]] * 15 +  # 第三张卡 x15
        [base_cards[3]] * 10    # 第四张卡 x10
    )

# 初始化每个猫娘的完整牌库
for catgirl_name, catgirl_data in CATGIRLS.items():
    catgirl_data["cards"] = generate_enemy_deck(catgirl_data["base_cards"])

LOCATIONS = {
    "学校": {
        "description": "放学后的校舍，夕阳将走廊染成暧昧的橘红色。空气中弥漫着粉笔灰和少女残留的体香。在这里，一切行动都显得鬼鬼祟祟。",
        "effect": lambda player, enemy: setattr(player, 'energy', player.energy + 1)
    },
    "游泳馆": {
        "description": "空无一人的游泳馆，水面反射着惨白灯光，消毒水的味道刺激着鼻腔。水声会掩盖你的脚步，但也可能让你滑倒。",
        "effect": lambda player, enemy: player.add_effect('dodge', 2, 0.15)
    },
    "公园": {
        "description": "黄昏的公园，树影幢幢，仿佛有无数眼睛在暗中窥视。这里是野性的主场，充满了不可预知的危险。",
        "effect": lambda player, enemy: enemy.add_effect('damage_bonus', 99, 2)
    }
}

# --- 玩家卡牌库定义 ---

# 基础卡牌定义
PLAYER_COMBAT_BASE_CARDS = [
    {"name": "尾随", "cost": 1, "type": "skill", "description": "你的下一次攻击额外造成4点伤害。"},
    {"name": "猛扑", "cost": 2, "type": "attack", "value": 8, "description": "对目标造成8点伤害。"},
    {"name": "擒抱", "cost": 3, "type": "attack", "value": 12, "description": "对目标造成12点伤害。"},
    {"name": "观察", "cost": 0, "type": "skill", "description": "抽一张牌。"},
    {"name": "格挡", "cost": 1, "type": "defense", "value": 7, "description": "获得7点护盾。"},
    {"name": "重整架势", "cost": 2, "type": "defense", "value": 12, "description": "获得12点护盾。"},
    {"name": "圈套", "cost": 2, "type": "skill", "description": "目标下回合攻击伤害减半。"},
    {"name": "麻醉剂", "cost": 4, "type": "skill", "description": "目标晕眩一回合，无法行动。"},
    {"name": "深呼吸", "cost": 0, "type": "skill", "description": "费用上限+1，费用+1。此牌被消耗。"},
]

# 构建50张卡的牌库 - 合理分配数量
PLAYER_COMBAT_DECK = (
    [PLAYER_COMBAT_BASE_CARDS[0]] * 6 +  # 尾随 x6
    [PLAYER_COMBAT_BASE_CARDS[1]] * 8 +  # 猛扑 x8
    [PLAYER_COMBAT_BASE_CARDS[2]] * 4 +  # 擒抱 x4
    [PLAYER_COMBAT_BASE_CARDS[3]] * 8 +  # 观察 x8
    [PLAYER_COMBAT_BASE_CARDS[4]] * 10 + # 格挡 x10
    [PLAYER_COMBAT_BASE_CARDS[5]] * 6 +  # 重整架势 x6
    [PLAYER_COMBAT_BASE_CARDS[6]] * 4 +  # 圈套 x4
    [PLAYER_COMBAT_BASE_CARDS[7]] * 2 +  # 麻醉剂 x2
    [PLAYER_COMBAT_BASE_CARDS[8]] * 2    # 深呼吸 x2
)

# 基础性爱卡牌定义
PLAYER_SEX_BASE_CARDS = [
    {"name": "试探性插入", "cost": 1, "type": "insert", "description": "注意力上限+1。"},
    {"name": "浅尝辄止", "cost": 1, "type": "insert", "description": "注意力上限+1。"},
    {"name": "初步接触", "cost": 2, "type": "insert", "description": "注意力上限+1。"},
    {"name": "轻柔进入", "cost": 2, "type": "insert", "description": "注意力上限+1。"},
    {"name": "深入探索", "cost": 3, "type": "insert", "description": "注意力上限+1。"},
    {"name": "寻幽探秘", "cost": 3, "type": "insert", "description": "注意力上限+1。"},
    {"name": "直捣黄龙", "cost": 4, "type": "insert", "description": "注意力上限+1。"},
    {"name": "猛烈冲击", "cost": 4, "type": "insert", "description": "注意力上限+1。"},
    {"name": "抚摸", "cost": 1, "type": "skill", "description": "抽一张牌。"},
    {"name": "亲吻", "cost": 2, "type": "skill", "description": "抽两张牌。"},
    {"name": "言语羞辱", "cost": 0, "type": "skill", "description": "本回合所有'插入'牌费用-1。"},
]

# 构建50张卡的性爱牌库
PLAYER_SEX_DECK = (
    [PLAYER_SEX_BASE_CARDS[0]] * 6 +  # 试探性插入 x6
    [PLAYER_SEX_BASE_CARDS[1]] * 6 +  # 浅尝辄止 x6
    [PLAYER_SEX_BASE_CARDS[2]] * 6 +  # 初步接触 x6
    [PLAYER_SEX_BASE_CARDS[3]] * 6 +  # 轻柔进入 x6
    [PLAYER_SEX_BASE_CARDS[4]] * 4 +  # 深入探索 x4
    [PLAYER_SEX_BASE_CARDS[5]] * 4 +  # 寻幽探秘 x4
    [PLAYER_SEX_BASE_CARDS[6]] * 3 +  # 直捣黄龙 x3
    [PLAYER_SEX_BASE_CARDS[7]] * 3 +  # 猛烈冲击 x3
    [PLAYER_SEX_BASE_CARDS[8]] * 6 +  # 抚摸 x6
    [PLAYER_SEX_BASE_CARDS[9]] * 3 +  # 亲吻 x3
    [PLAYER_SEX_BASE_CARDS[10]] * 3   # 言语羞辱 x3
)

EJACULATE_CARD_TEMPLATE = {"name": "内射", "cost": 8, "type": "ejaculate", "description": "重置注意力上限为3。此牌无法被丢弃。", "undiscardable": True}

# 基础抵抗卡牌定义
ENEMY_RESISTANCE_BASE_CARDS = [
    {"name": "抵抗", "cost": 2, "type": "resistance", "description": "你的注意力上限-1。"},
    {"name": "哭泣", "cost": 3, "type": "resistance", "description": "弃掉你所有手牌。"},
    {"name": "抓挠", "cost": 2, "type": "resistance", "description": "你失去所有注意力。"},
    {"name": "蜷缩身体", "cost": 4, "type": "resistance", "description": "一回合内，你无法打出'插入'牌。"},
]

# 构建50张卡的抵抗牌库
ENEMY_RESISTANCE_DECK = (
    [ENEMY_RESISTANCE_BASE_CARDS[0]] * 20 + # 抵抗 x20
    [ENEMY_RESISTANCE_BASE_CARDS[1]] * 10 + # 哭泣 x10
    [ENEMY_RESISTANCE_BASE_CARDS[2]] * 15 + # 抓挠 x15
    [ENEMY_RESISTANCE_BASE_CARDS[3]] * 5    # 蜷缩身体 x5
)

# --- 游戏引擎核心类 ---

class Entity:
    def __init__(self, name, hp, deck_data, is_player=False):
        self.name = name
        self.max_hp = hp
        self.hp = hp
        self.shield = 0
        self.energy = 3
        self.max_energy = 3
        self.attention = 3
        self.max_attention = 3
        self.deck = list(deck_data)
        self.hand = []
        self.discard_pile = []
        self.effects = {}
        self.is_player = is_player
        random.shuffle(self.deck)

    def add_effect(self, effect_name, duration, value):
        self.effects[effect_name] = {"duration": duration, "value": value}

    def tick_effects(self):
        if 'dot' in self.effects:
            dot = self.effects['dot']
            self.take_damage(dot['value'], None) # Pass None for game, as it's not used in take_damage for dot
            dot['duration'] -= 1
            if dot['duration'] <= 0:
                del self.effects['dot']

    def end_of_turn_effects(self, game):
        # 处理发情卡的回合结束伤害
        if not self.is_player:
            arousal_count = len([c for c in self.hand if c['name'] == '发情'])
            if arousal_count > 0:
                damage = arousal_count * 2
                self.hp -= damage  # 发情伤害无视护盾
                self.hp = max(0, self.hp)
                game.add_history(f"【发情】{self.name}因手牌中的{arousal_count}张发情卡失去{damage}点生命！")

        # 处理其他效果
        effects_to_remove = [k for k, v in self.effects.items() if (v['duration'] - 1) <= 0 and k not in ['dot']]
        for effect in effects_to_remove:
            del self.effects[effect]
        for k, v in self.effects.items():
             if k not in ['dot']:
                v['duration'] -=1


    def draw_card(self, game, quantity=1):
        for _ in range(quantity):
            if not self.deck:
                # 牌库空了，只能抽到惩罚卡
                penalty_card = PENALTY_CARD_PLAYER if self.is_player else PENALTY_CARD_ENEMY
                self.hand.append(penalty_card.copy())
                game.add_history(f"【警告】{self.name}的牌库已空，抽到惩罚卡：【{penalty_card['name']}】！")
            else:
                # 从牌库抽卡
                card = self.deck.pop(0)
                self.hand.append(card)

    def take_damage(self, amount, game):
        if 'dodge' in self.effects and random.random() < self.effects['dodge']['value']:
            game.add_history(f"【闪避】{self.name}躲开了攻击！")
            return

        if 'damage_debuff' in self.effects:
            amount = int(amount * self.effects['damage_debuff']['value'])
            game.add_history(f"【效果】{self.name}的攻击被削弱了！")

        final_amount = amount
        if self.shield > 0:
            self.shield -= final_amount
            if self.shield < 0:
                self.hp += self.shield
                self.shield = 0
        else:
            self.hp -= final_amount
        self.hp = max(0, self.hp)

    def add_shield(self, amount, game):
        if 'no_shield' in self.effects:
            game.add_history(f"【效果】{self.name}被【湿漉漉的眼神】影响，无法获得护盾！")
            return
        self.shield += amount
        game.add_history(f"【防御】{self.name}获得了 {amount} 点护盾。")


    def start_turn(self, phase):
        if phase == 1:
            self.energy = self.max_energy
            self.shield = 0
        else:
            self.attention = self.max_attention
        self.tick_effects()

class Game:
    def __init__(self):
        self.player = None
        self.enemy = None
        self.location = None
        self.turn = 0
        self.phase = 1
        self.game_over = False
        self.global_history = [] # 用于永久记录的事件历史
        self.resistance_will = 50
        self.max_resistance_will = 50
        self.can_get_ejaculate_card = True
        self.is_first_insert_of_phase = True

    def add_history(self, message):
        # 这个函数现在只用于后台记录，不再直接显示
        if isinstance(message, list):
            self.global_history.extend(message)
        else:
            self.global_history.append(message)
        
        while len(self.global_history) > 6:
            self.global_history.pop(0)

    def clear_screen(self):
        os.system('cls' if os.name == 'nt' else 'clear')

    def print_slow(self, text, delay=0.05):
        for char in text:
            print(char, end='', flush=True)
            time.sleep(delay)
        print()

    def show_action_and_pause(self, results):
        """在当前屏幕下方打印结算过程，并停顿"""
        print("\n---")
        for result in results:
            print(f"> {result}")
        time.sleep(1.5)

    def format_cost(self, cost):
        return "(-)" if cost == 0 else f"({cost})"

    def format_resource_bar(self, entity, resource_type):
        if resource_type == 'energy':
            current, maximum, name = entity.energy, entity.max_energy, "费用"
        else:
            current, maximum, name = entity.attention, entity.max_attention, "注意力"
        bar = '■' * current + '□' * (maximum - current)
        return f"{name}: {bar} ({current}/{maximum})"

    def setup_game(self):
        self.clear_screen()
        self.print_slow("夜色如墨，欲望的狩猎即将开始...")
        catgirl_options = list(CATGIRLS.keys())
        for i, name in enumerate(catgirl_options):
            print(f"{i+1}. {name}: {CATGIRLS[name]['description']}")
        choice = -1
        while choice not in range(len(catgirl_options)):
            try: choice = int(input("输入选择的数字: ")) - 1
            except ValueError: pass
        chosen_catgirl_name = catgirl_options[choice]
        chosen_catgirl_data = CATGIRLS[chosen_catgirl_name]
        
        self.clear_screen()
        location_options = list(LOCATIONS.keys())
        for i, name in enumerate(location_options):
            print(f"{i+1}. {name}: {LOCATIONS[name]['description']}")
        choice = -1
        while choice not in range(len(location_options)):
            try: choice = int(input("输入选择的数字: ")) - 1
            except ValueError: pass
        chosen_location_name = location_options[choice]
        self.location = LOCATIONS[chosen_location_name]

        self.player = Entity("你", 100, PLAYER_COMBAT_DECK, is_player=True)
        self.enemy = Entity(chosen_catgirl_name, chosen_catgirl_data['hp'], chosen_catgirl_data['cards'])
        
        # 初始抽卡
        for _ in range(4):
            self.player.draw_card(self)
            self.enemy.draw_card(self)

        self.location['effect'](self.player, self.enemy)
        self.clear_screen()
        self.print_slow(f"在《{chosen_location_name}》，你与 {chosen_catgirl_name} 的追逐开始了...")
        time.sleep(2)

    def display_game_state(self):
        self.clear_screen()
        if self.phase == 1:
            print(f"--- 追逐战 --- 回合: {self.turn} --- 场景: {list(LOCATIONS.keys())[list(LOCATIONS.values()).index(self.location)]} ---")
            print(f"\n{self.enemy.name}:")
            print(f"  生命: {self.enemy.hp}/{self.enemy.max_hp}  护盾: {self.enemy.shield}  {self.format_resource_bar(self.enemy, 'energy')}")
            print(f"  牌库: {len(self.enemy.deck)} | 弃牌堆: {len(self.enemy.discard_pile)}")
            print("-" * 40)
            print(f"{self.player.name}:")
            print(f"  生命: {self.player.hp}/{self.player.max_hp}  护盾: {self.player.shield}  {self.format_resource_bar(self.player, 'energy')}")
            print(f"  牌库: {len(self.player.deck)} | 弃牌堆: {len(self.player.discard_pile)}")
        else:
            print(f"--- 性爱阶段 --- 回合: {self.turn}/50 ---")
            print(f"\n{self.enemy.name} 在你的身下: ")
            print(f"  抵抗意志: {self.resistance_will}/{self.max_resistance_will}  {self.format_resource_bar(self.enemy, 'attention')}")
            print(f"  牌库: {len(self.enemy.deck)} | 弃牌堆: {len(self.enemy.discard_pile)}")
            print("-" * 40)
            print(f"{self.player.name}:")
            print(f"  {self.format_resource_bar(self.player, 'attention')}")
            print(f"  牌库: {len(self.player.deck)} | 弃牌堆: {len(self.player.discard_pile)}")

        print("\n--- 事件记录 ---")
        if not self.global_history: print(" (空)")
        else:
            for entry in self.global_history: print(f" > {entry}")
        
        print("\n--- 你的手牌 ---")
        for i, card in enumerate(self.player.hand):
            cost_str = self.format_cost(card['cost'])
            print(f"  {i+1}: [{card['name']}] {cost_str} - {card['description']}")

    def transition_to_phase2(self):
        self.phase = 2
        self.clear_screen()
        self.print_slow(f"随着一声呜咽，{self.enemy.name}失去了所有抵抗的力量，倒在了你的面前...")
        time.sleep(1)
        self.print_slow("追逐结束了。但真正的“游戏”，现在才刚刚开始。")
        time.sleep(2)
        self.add_history(f"{self.enemy.name}的抵抗意志被削弱了。")

        self.player.deck = list(PLAYER_SEX_DECK)
        self.player.discard_pile, self.player.hand = [], []
        random.shuffle(self.player.deck)

        self.enemy.deck = list(ENEMY_RESISTANCE_DECK)
        self.enemy.discard_pile, self.enemy.hand = [], []
        random.shuffle(self.enemy.deck)
        
        self.player.attention, self.player.max_attention = 3, 3
        self.enemy.attention, self.enemy.max_attention = 3, 3
        self.turn = 0
        
        for _ in range(5): self.player.draw_card(self)
        for _ in range(3): self.enemy.draw_card(self)

    def play_card_phase1(self, card_index):
        action_results = []
        if not (0 <= card_index < len(self.player.hand)): return action_results
        card = self.player.hand[card_index]
        if self.player.energy < card['cost']: 
            action_results.append("费用不足。")
            return action_results

        played_card = self.player.hand.pop(card_index)
        self.player.energy -= played_card['cost']
        action_results.append(f"【出牌】你打出了 [{played_card['name']}]!")
        
        # --- 卡牌效果实现 ---
        if played_card['name'] == '尾随': 
            self.player.add_effect('damage_bonus', 2, 4)
            action_results.append("【效果】你的下一次攻击将造成额外4点伤害。")
        elif played_card['type'] == 'attack':
            damage = played_card['value']
            if 'damage_bonus' in self.player.effects:
                damage += self.player.effects.pop('damage_bonus')['value']
                action_results.append(f"【效果】消耗【尾随】效果，伤害提升！")
            self.enemy.take_damage(damage, self)
            action_results.append(f"【伤害】对{self.enemy.name}造成了 {damage} 点伤害！")
        elif played_card['name'] == '观察': 
            self.player.draw_card(self)
            action_results.append("【抽牌】你抽了一张牌。")
        elif played_card['type'] == 'defense': 
            self.player.add_shield(played_card['value'], self)
            action_results.append(f"【防御】你获得了 {played_card['value']} 点护盾。")
        elif played_card['name'] == '圈套': 
            self.enemy.add_effect('damage_debuff', 2, 0.5)
            action_results.append(f"【效果】{self.enemy.name}下回合攻击伤害减半！")
        elif played_card['name'] == '麻醉剂': 
            self.enemy.add_effect('stun', 2, 1)
            action_results.append(f"【效果】{self.enemy.name}被麻醉了！")
        elif played_card['name'] == '深呼吸':
            self.player.max_energy += 1
            self.player.energy += 1
            action_results.append("【效果】你的费用上限+1，费用+1。")
        elif played_card['name'] == '围观群众':
            # 强制打出围观群众的惩罚
            self.player.hp -= 20  # 无视护盾
            self.player.hp = max(0, self.player.hp)
            action_results.append("【惩罚】强行打出围观群众，你失去20点生命！")

        if played_card['name'] != '深呼吸':
            self.player.discard_pile.append(played_card)
        
        return action_results

    def play_card_phase2(self, card_index):
        action_results = []
        if not (0 <= card_index < len(self.player.hand)): return action_results
        card = self.player.hand[card_index]
        if self.player.attention < card['cost']: 
            action_results.append("注意力不足。")
            return action_results
        if 'cant_play_insert' in self.player.effects and card['type'] == 'insert':
            action_results.append("【效果】你现在无法打出'插入'牌！")
            return action_results

        played_card = self.player.hand.pop(card_index)
        self.player.attention -= played_card['cost']
        action_results.append(f"【出牌】你打出了 [{played_card['name']}]!")

        if played_card['type'] == 'insert':
            self.player.max_attention += 1
            self.player.attention += 1
            action_results.append("你的注意力上限+1，注意力+1。")
            if self.can_get_ejaculate_card:
                self.player.hand.append(EJACULATE_CARD_TEMPLATE.copy())
                action_results.append("【获得】你获得了一张【内射】。")
                self.can_get_ejaculate_card = False
            self.is_first_insert_of_phase = False
        elif played_card['type'] == 'ejaculate':
            self.player.max_attention = 3
            self.player.attention = 3
            self.can_get_ejaculate_card = True
            action_results.append("在一阵颤抖后，你的注意力上限重置为3。")
        elif played_card['name'] == '抚摸': 
            self.player.draw_card(self)
            action_results.append("【抽牌】你抽了一张牌。")
        elif played_card['name'] == '亲吻': 
            self.player.draw_card(self, 2)
            action_results.append("【抽牌】你抽了两张牌。")
        elif played_card['name'] == '言语羞辱': 
            self.player.add_effect('insert_cost_down', 1, 1)
            action_results.append("【效果】本回合'插入'牌费用-1。")

        if not played_card.get('consumed', False) and played_card['name'] != '内射':
            self.player.discard_pile.append(played_card)
        
        return action_results

    def enemy_turn_phase1(self):
        self.enemy.start_turn(self.phase)

        # 敌人回合开始时抽牌（第一回合除外，因为已经有初始手牌）
        if self.turn > 1:
            self.enemy.draw_card(self)

        if 'stun' in self.enemy.effects:
            results = [f"【效果】{self.enemy.name}处于晕眩中，无法行动！"]
            self.show_action_and_pause(results)
            self.add_history(results)
        else:
            # 检查是否需要处理发情卡
            arousal_cards = [c for c in self.enemy.hand if c['name'] == '发情']
            arousal_damage = len(arousal_cards) * 2

            # 如果回合结束时发情伤害会击倒自己，优先打出所有发情卡
            if arousal_damage >= self.enemy.hp:
                # 打出所有发情卡直到被击倒
                for arousal_card in arousal_cards:
                    if self.enemy.hp <= 0:
                        break
                    turn_results = []
                    self.enemy.hand.remove(arousal_card)
                    turn_results.append(f"【强制出牌】{self.enemy.name}被迫打出了 [{arousal_card['name']}]!")
                    # 发情卡强制打出的惩罚
                    self.enemy.hp -= 6  # 无视护盾
                    self.enemy.hp = max(0, self.enemy.hp)
                    turn_results.append(f"【惩罚】{self.enemy.name}因强行打出发情卡失去6点生命！")
                    self.enemy.discard_pile.append(arousal_card)
                    self.show_action_and_pause(turn_results)
                    self.add_history(turn_results)
                    if self.enemy.hp <= 0:
                        return
            else:
                # 正常出牌逻辑，但跳过发情卡
                played_a_card = True
                while played_a_card:
                    played_a_card = False
                    card_to_play_index = -1
                    for i, card in enumerate(self.enemy.hand):
                        if self.enemy.energy >= card['cost'] and card['name'] != '发情':
                            card_to_play_index = i
                            break

                    if card_to_play_index != -1:
                        turn_results = []
                        played_card = self.enemy.hand.pop(card_to_play_index)
                        self.enemy.energy -= played_card['cost']
                        turn_results.append(f"【出牌】{self.enemy.name}打出了 [{played_card['name']}]!")

                        if played_card['type'] == 'attack':
                            damage = played_card['value']
                            if 'damage_bonus' in self.enemy.effects:
                                damage += self.enemy.effects['damage_bonus']['value']
                            self.player.take_damage(damage, self)
                            turn_results.append(f"【伤害】你受到了 {damage} 点伤害！")
                        elif played_card['type'] == 'defense':
                            self.enemy.add_shield(played_card['value'], self)
                            turn_results.append(f"【防御】{self.enemy.name}获得了 {played_card['value']} 点护盾。")
                        elif played_card['name'] == '撒娇':
                            if self.player.hand:
                                discarded = random.choice(self.player.hand)
                                self.player.hand.remove(discarded)
                                self.player.discard_pile.append(discarded)
                                turn_results.append(f"【弃牌】你的【{discarded['name']}】被弃掉了！")

                        self.enemy.discard_pile.append(played_card)
                        self.show_action_and_pause(turn_results)
                        self.add_history(turn_results)
                        played_a_card = True
                        if self.player.hp <= 0: return

        self.enemy.end_of_turn_effects(self)
        self.add_history(f"{self.enemy.name}结束了回合。")

    def enemy_turn_phase2(self):
        self.enemy.start_turn(self.phase)

        # 敌人回合开始时抽牌（第一回合除外，因为已经有初始手牌）
        if self.turn > 1:
            self.enemy.draw_card(self)

        played_a_card = True
        while played_a_card:
            played_a_card = False
            card_to_play_index = -1
            for i, card in enumerate(self.enemy.hand):
                if self.enemy.attention >= card['cost']:
                    card_to_play_index = i
                    break

            if card_to_play_index != -1:
                turn_results = []
                played_card = self.enemy.hand.pop(card_to_play_index)
                self.enemy.attention -= played_card['cost']
                turn_results.append(f"【抵抗】{self.enemy.name}打出了 [{played_card['name']}]!")

                if played_card['name'] == '抵抗': 
                    self.player.max_attention = max(1, self.player.max_attention - 1)
                    turn_results.append("【效果】你的注意力上限-1。")
                elif played_card['name'] == '哭泣':
                    self.player.discard_pile.extend(self.player.hand)
                    self.player.hand.clear()
                    turn_results.append("【效果】你所有的手牌都被弃掉了！")
                elif played_card['name'] == '抓挠': 
                    self.player.attention = 0
                    turn_results.append("【效果】你失去了所有注意力。")
                elif played_card['name'] == '蜷缩身体': 
                    self.player.add_effect('cant_play_insert', 2, 1)
                    turn_results.append("【效果】你暂时无法使用'插入'牌。")
                
                self.enemy.discard_pile.append(played_card)
                self.show_action_and_pause(turn_results)
                self.add_history(turn_results)
                played_a_card = True

        self.enemy.end_of_turn_effects(self)
        self.add_history(f"{self.enemy.name}结束了回合。")

    def run(self):
        self.setup_game()
        while not self.game_over:
            if self.phase == 1: self.run_phase1()
            else: self.run_phase2()

    def run_phase1(self):
        self.turn += 1
        self.player.start_turn(self.phase)

        # 玩家回合开始时抽牌（第一回合除外，因为已经有初始手牌）
        if self.turn > 1:
            self.player.draw_card(self)
        
        player_turn_over = False
        while not player_turn_over:
            self.display_game_state()
            action = input("\n选择一张牌 (输入数字), 或 'end' 结束回合: ")
            if action.lower() == 'end':
                player_turn_over = True
                continue
            elif action == 'enemy_lib_clr':
                self.enemy.deck.clear()
                continue
            elif action == 'player_lib_clr':
                self.player.deck.clear()
                continue
            elif action == 'kill_enemy':
                self.enemy.hp = 0
                player_turn_over = True
                continue
            elif action == 'kill_player':
                self.player.hp = 0
                player_turn_over = True
                continue
            elif action.startswith('set_enemy_cost '):
                try:
                    cost = int(action.split()[1])
                    self.enemy.max_energy = cost
                    self.enemy.energy = cost
                except:
                    pass
                continue
            elif action.startswith('set_player_cost '):
                try:
                    cost = int(action.split()[1])
                    self.player.max_energy = cost
                    self.player.energy = cost
                except:
                    pass
                continue

            try:
                card_index = int(action) - 1
                action_results = self.play_card_phase1(card_index)
                if action_results:
                    self.show_action_and_pause(action_results)
                    self.add_history(action_results)
                    if self.enemy.hp <= 0:
                        player_turn_over = True
            except (ValueError, IndexError):
                pass

        self.add_history("你结束了回合。")
        self.show_action_and_pause(["你结束了回合。"])
        self.player.end_of_turn_effects(self)

        if len([c for c in self.player.hand if c['name'] == '围观群众']) >= 5:
            self.game_over = True; print("\n【游戏结束】罪行暴露。")
            return
        if self.enemy.hp <= 0: 
            self.transition_to_phase2()
            return
        
        self.enemy_turn_phase1()
        if self.player.hp <= 0: self.game_over = True; print("\n【游戏结束】你失败了。")

    def run_phase2(self):
        self.turn += 1
        self.player.start_turn(self.phase)

        # 玩家回合开始时抽牌补充到5张（第一回合除外，因为已经有初始手牌）
        if self.turn > 1:
            self.player.draw_card(self, 5 - len(self.player.hand))
        
        player_turn_over = False
        while not player_turn_over:
            self.display_game_state()
            action = input("\n选择一张牌 (输入数字), 或 'end' 结束回合, 'finish' 提前结算: ")
            if action.lower() == 'end':
                player_turn_over = True
                continue
            elif action.lower() == 'finish':
                self.game_over = True; print("\n【结算】你决定提前结束...")
                return
            elif action == 'enemy_lib_clr':
                self.enemy.deck.clear()
                continue
            elif action == 'player_lib_clr':
                self.player.deck.clear()
                continue
            elif action == 'kill_enemy':
                self.resistance_will = 0
                self.game_over = True
                return
            elif action == 'kill_player':
                self.player.hp = 0
                self.game_over = True
                return
            elif action.startswith('set_enemy_cost '):
                try:
                    cost = int(action.split()[1])
                    self.enemy.max_attention = cost
                    self.enemy.attention = cost
                except:
                    pass
                continue
            elif action.startswith('set_player_cost '):
                try:
                    cost = int(action.split()[1])
                    self.player.max_attention = cost
                    self.player.attention = cost
                except:
                    pass
                continue

            try:
                card_index = int(action) - 1
                action_results = self.play_card_phase2(card_index)
                if action_results:
                    self.show_action_and_pause(action_results)
                    self.add_history(action_results)
            except (ValueError, IndexError):
                pass

        self.resistance_will -= 1
        end_turn_results = [f"【猥亵】回合结束，{self.enemy.name}的抵抗意志-1。"]
        self.player.end_of_turn_effects(self)
        self.show_action_and_pause(end_turn_results)
        self.add_history(end_turn_results)

        if self.resistance_will <= 0:
            self.game_over = True; print(f"\n【胜利】{self.enemy.name}彻底屈服了。")
            return
        if self.turn >= 50:
            self.game_over = True; print("\n【结算】50回合结束。")
            return

        self.enemy_turn_phase2()


if __name__ == "__main__":
    game = Game()
    game.run()
